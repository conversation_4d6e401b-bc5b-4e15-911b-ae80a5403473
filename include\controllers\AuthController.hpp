#pragma once

#include "services/AuthService.hpp"
#include <httplib.h>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>

class Application;

class AuthController
{
public:
    AuthController(Application &app);

private:
    void login(const httplib::Request &req, httplib::Response &res);
    void logout(const httplib::Request &req, httplib::Response &res);
    void checkAuth(const httplib::Request &req, httplib::Response &res);

    Application &app_;
    std::shared_ptr<AuthService> auth_service_;

    std::string getSessionToken(const httplib::Request &req);
    void setSessionCookie(httplib::Response &res, const std::string &token);
    void clearSessionCookie(httplib::Response &res);
};

// Forward declaration to avoid circular dependency
#include "core/Application.hpp"

// Inline implementations
inline AuthController::AuthController(Application &app)
    : app_(app)
{
    auth_service_ = app.getAuthService();
    auto &server = app.getServer();

    // Register auth routes
    server.Post("/api/auth/login", [this](const httplib::Request &req, httplib::Response &res) { login(req, res); });
    server.Post("/api/auth/logout", [this](const httplib::Request &req, httplib::Response &res) { logout(req, res); });
    server.Get("/api/auth/check", [this](const httplib::Request &req, httplib::Response &res) { checkAuth(req, res); });
}

inline void AuthController::login(const httplib::Request &req, httplib::Response &res)
{
    try
    {
        auto json_body = nlohmann::json::parse(req.body);
        std::string username = json_body.value("username", "");
        std::string password = json_body.value("password", "");

        if (username.empty() || password.empty())
        {
            res.status = 400;
            res.set_content(R"({"code": 1, "message": "Username and password are required"})", "application/json");
            return;
        }

        std::string token = auth_service_->authenticate(username, password);
        if (!token.empty())
        {
            setSessionCookie(res, token);
            res.set_content(R"({"code": 0, "message": "Login successful"})", "application/json");
        }
        else
        {
            res.status = 401;
            res.set_content(R"({"code": 1, "message": "Invalid username or password"})", "application/json");
        }
    }
    catch (const std::exception &e)
    {
        SPDLOG_ERROR("Login error: {}", e.what());
        res.status = 500;
        res.set_content(R"({"code": 1, "message": "Internal server error"})", "application/json");
    }
}

inline void AuthController::logout(const httplib::Request &req, httplib::Response &res)
{
    std::string token = getSessionToken(req);
    if (!token.empty())
    {
        auth_service_->invalidateSession(token);
    }

    clearSessionCookie(res);
    res.set_content(R"({"code": 0, "message": "Logout successful"})", "application/json");
}

inline void AuthController::checkAuth(const httplib::Request &req, httplib::Response &res)
{
    std::string token = getSessionToken(req);
    bool authenticated = !token.empty() && auth_service_->validateSession(token);

    if (authenticated)
    {
        res.set_content(R"({"code": 0, "authenticated": true})", "application/json");
    }
    else
    {
        res.set_content(R"({"code": 0, "authenticated": false})", "application/json");
    }
}

inline std::string AuthController::getSessionToken(const httplib::Request &req)
{
    // Try to get token from cookie first
    auto cookie_header = req.get_header_value("Cookie");
    if (!cookie_header.empty())
    {
        size_t pos = cookie_header.find("session_token=");
        if (pos != std::string::npos)
        {
            pos += 14; // length of "session_token="
            size_t end = cookie_header.find(';', pos);
            if (end == std::string::npos)
                end = cookie_header.length();
            return cookie_header.substr(pos, end - pos);
        }
    }

    return "";
}

inline void AuthController::setSessionCookie(httplib::Response &res, const std::string &token)
{
    // Set cookie with 24 hour expiration and secure flags
    std::string cookie = "session_token=" + token + "; Path=/; Max-Age=86400; HttpOnly; SameSite=Strict";
    res.set_header("Set-Cookie", cookie);
}

inline void AuthController::clearSessionCookie(httplib::Response &res)
{
    std::string cookie = "session_token=; Path=/; Max-Age=0; HttpOnly; SameSite=Strict";
    res.set_header("Set-Cookie", cookie);
}
