#!/usr/bin/env python3
import hashlib
import sys

def hash_password(password):
    """Generate SHA-256 hash of password"""
    return hashlib.sha256(password.encode('utf-8')).hexdigest()

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print(f"Usage: {sys.argv[0]} <password>")
        print(f"Example: {sys.argv[0]} admin123")
        sys.exit(1)
    
    password = sys.argv[1]
    hash_value = hash_password(password)
    
    print(f"Password: {password}")
    print(f"SHA-256 Hash: {hash_value}")
