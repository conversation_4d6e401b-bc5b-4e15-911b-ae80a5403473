#pragma once

#include <algorithm>
#include <httplib.h>
#include <spdlog/spdlog.h>
#include <unordered_map>
#include <fstream>
#include "core/Application.hpp"

#ifdef EMBED_RESOURCES
    #include "resources/EmbeddedResources.hpp"
#else
    #include <filesystem>
    #include <fstream>
#endif

class StaticController
{
public:
    StaticController(Application &app);

private:
    Application &app_;

    void handleRoot(const httplib::Request &req, httplib::Response &res);
    void handleStaticFile(const httplib::Request &req, httplib::Response &res);
    bool isAuthenticated(const httplib::Request &req);
    std::string getSessionToken(const httplib::Request &req);

    static void serveFile(const std::string &path, httplib::Response &res)
    {
#ifdef EMBED_RESOURCES
        serveEmbeddedFile(path, res);
#else
        serveFileFromDisk(path, res);
#endif
    }

#ifdef EMBED_RESOURCES
    static void serveEmbeddedFile(const std::string &path, httplib::Response &res)
    {
        try
        {
            const auto *resource = EmbeddedResources::getResource(path);
            if (resource)
            {
                res.status = 200;
                res.set_header("Content-Type", resource->mime_type);
                res.set_header("Cache-Control", "public, max-age=3600");
                res.body = std::string(reinterpret_cast<const char *>(resource->data), resource->size);

                SPDLOG_DEBUG("Serving embedded resource: {}", path);
                return;
            }

            res.status = 404;
            res.body = "File not found";
        }
        catch (const std::exception &e)
        {
            SPDLOG_ERROR("Error serving embedded resource {}: {}", path, e.what());
            res.status = 500;
            res.body = "Internal server error";
        }
    }
#endif

    static void serveFileFromDisk(const std::string &path, httplib::Response &res)
    {
        try
        {
            std::string file_path = "web" + path;

            // Security check: prevent directory traversal
            if (path.find("..") != std::string::npos)
            {
                SPDLOG_WARN("Directory traversal attempt: {}", path);
                res.status = 403;
                res.body = "Forbidden";
                return;
            }

            if (!std::filesystem::exists(file_path))
            {
                res.status = 404;
                res.body = "File not found";
                return;
            }

            std::ifstream file(file_path, std::ios::binary);
            if (!file.is_open())
            {
                SPDLOG_ERROR("Cannot open file: {}", file_path);
                res.status = 500;
                res.body = "Cannot open file";
                return;
            }

            std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());

            res.status = 200;
            res.set_header("Content-Type", getMimeType(path));
            res.set_header("Cache-Control", "public, max-age=3600");
            res.body = content;
        }
        catch (const std::exception &e)
        {
            SPDLOG_ERROR("Error serving file {}: {}", path, e.what());
            res.status = 500;
            res.body = "Internal server error";
        }
    }

    static std::string getMimeType(const std::string &path)
    {
        std::filesystem::path file_path(path);
        std::string ext = file_path.extension().string();
        std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);

        static const std::unordered_map<std::string, std::string> mime_types = {
            { ".html", "text/html" },
            { ".css", "text/css" },
            { ".js", "application/javascript" },
            { ".json", "application/json" },
            { ".png", "image/png" },
            { ".jpg", "image/jpeg" },
            { ".jpeg", "image/jpeg" },
            { ".gif", "image/gif" },
            { ".svg", "image/svg+xml" },
            { ".ico", "image/x-icon" },
            { ".txt", "text/plain" },
        };

        auto it = mime_types.find(ext);
        return (it != mime_types.end()) ? it->second : "application/octet-stream";
    }
};

// Inline implementations
inline StaticController::StaticController(Application &app)
    : app_(app)
{
    auto &server = app.getServer();

    // Serve index.html at root with authentication check
    server.Get("/", [this](const httplib::Request &req, httplib::Response &res) {
        handleRoot(req, res);
    });

    // Serve static files - but exclude API paths
    server.Get(R"(/(.+))", [this](const httplib::Request &req, httplib::Response &res) {
        handleStaticFile(req, res);
    });
}

inline void StaticController::handleRoot(const httplib::Request &req, httplib::Response &res)
{
    if (isAuthenticated(req))
    {
        serveFile("/index.html", res);
    }
    else
    {
        // Redirect to login.html instead of serving inline content
        res.status = 302;
        res.set_header("Location", "/login.html");
    }
}

inline void StaticController::handleStaticFile(const httplib::Request &req, httplib::Response &res)
{
    auto matches = req.matches;
    std::string path = matches[1];

    // Don't serve API paths as static files
    if (path.substr(0, 4) == "api/")
    {
        res.status = 404;
        res.body = "API endpoint not found";
        return;
    }

    // Allow access to login.html without authentication
    if (path == "login.html")
    {
        serveFile("/" + path, res);
        return;
    }

    // Check authentication for other files
    if (!isAuthenticated(req))
    {
        res.status = 401;
        res.set_header("Content-Type", "text/html");
        res.body = R"(
<!DOCTYPE html>
<html>
<head>
    <title>Unauthorized</title>
</head>
<body>
    <h1>401 - Unauthorized</h1>
    <p>Please <a href="/">login</a> to access this resource.</p>
</body>
</html>)";
        return;
    }

    serveFile("/" + path, res);
}

inline bool StaticController::isAuthenticated(const httplib::Request &req)
{
    std::string token = getSessionToken(req);
    if (token.empty())
    {
        return false;
    }

    auto auth_service = app_.getAuthService();
    return auth_service && auth_service->validateSession(token);
}

inline std::string StaticController::getSessionToken(const httplib::Request &req)
{
    // Try to get token from cookie
    auto cookie_header = req.get_header_value("Cookie");
    if (!cookie_header.empty())
    {
        size_t pos = cookie_header.find("session_token=");
        if (pos != std::string::npos)
        {
            pos += 14; // length of "session_token="
            size_t end = cookie_header.find(';', pos);
            if (end == std::string::npos)
                end = cookie_header.length();
            return cookie_header.substr(pos, end - pos);
        }
    }

    return "";
}
