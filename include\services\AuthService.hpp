#pragma once

#include "config/Config.hpp"
#include <chrono>
#include <random>
#include <string>
#include <unordered_map>
#include <openssl/sha.h>
#include <iomanip>
#include <sstream>

class AuthService
{
public:
    AuthService(const AuthConfig &config);

    // Authenticate user with username and password
    std::string authenticate(const std::string &username, const std::string &password);

    // Validate session token
    bool validateSession(const std::string &token);

    // Invalidate session
    void invalidateSession(const std::string &token);

    // Generate session token
    std::string generateSessionToken();

    // Check if session is expired
    bool isSessionExpired(const std::string &token);

    // Hash password using SHA-256
    static std::string hashPassword(const std::string &password);

    // Verify password against hash
    bool verifyPassword(const std::string &password, const std::string &hash);

private:
    struct SessionInfo
    {
        std::chrono::system_clock::time_point created_at;
        std::chrono::system_clock::time_point expires_at;
    };

    AuthConfig config_;
    std::unordered_map<std::string, SessionInfo> sessions_;
    std::mt19937 rng_;

    void cleanupExpiredSessions();
};
