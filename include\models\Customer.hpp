#pragma once

#include "JsonUtil.hpp"
#include <string>

struct Customer
{
    std::string id;
    std::string contact;
    std::string department;
    std::string phones;
    std::string unit_name;
    std::string group_name;
};

void from_json(const nlohmann::json &j, Customer &p)
{
    p.id = std::to_string(JsonUtil::get<int>(j, "id"));
    p.unit_name = JsonUtil::get<std::string>(j, "unitName");
    p.contact = JsonUtil::get<std::string>(j, "contact");
    p.department = JsonUtil::get<std::string>(j, "department");
    p.phones = JsonUtil::get<std::string>(j, "phones");
    p.group_name = JsonUtil::get<std::string>(j, "groupName");
}

void to_json(nlohmann::json &j, const Customer &p)
{
    j = nlohmann::json{
        { "id", p.id },
        { "contact", p.contact },
        { "department", p.department },
        { "phones", p.phones },
        { "unit_name", p.unit_name },
        { "group_name", p.group_name },
    };
}