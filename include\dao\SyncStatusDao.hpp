﻿#pragma once

#include "database/DatabaseManager.hpp"
#include "models/SyncStatus.hpp"
#include <optional>
#include <spdlog/spdlog.h>
#include <vector>

class SyncStatusDao
{
public:
    SyncStatusDao(DatabaseManager &db_manager)
        : db_manager_(db_manager)
    {
    }

    std::vector<SyncStatus> findAll()
    {
        std::vector<SyncStatus> statuses;
        try
        {
            auto &db = db_manager_.getDatabase();
            SQLite::Statement query(db, "SELECT * FROM sync_status ORDER BY data_type");

            while (query.executeStep())
            {
                SyncStatus status;
                status.data_type = query.getColumn(0).getString();
                status.sync_time = query.getColumn(1).getInt64();
                statuses.push_back(status);
            }
        }
        catch (const std::exception &e)
        {
            SPDLOG_ERROR("SyncStatusDao::findAll error: {}", e.what());
        }
        return statuses;
    }

    std::optional<SyncStatus> findByDataType(const std::string &data_type)
    {
        try
        {
            auto &db = db_manager_.getDatabase();
            SQLite::Statement query(db, "SELECT * FROM sync_status WHERE data_type=?");
            query.bind(1, data_type);

            if (query.executeStep())
            {
                SyncStatus status;
                status.data_type = query.getColumn(0).getString();
                status.sync_time = query.getColumn(1).getInt64();
                return status;
            }
            return std::nullopt;
        }
        catch (const std::exception &e)
        {
            SPDLOG_ERROR("SyncStatusDao::findByDataType error: {}", e.what());
            return std::nullopt;
        }
    }

    bool insertOrUpdate(const SyncStatus &status)
    {
        try
        {
            auto &db = db_manager_.getDatabase();
            SQLite::Statement query(db, R"(
                INSERT OR REPLACE INTO sync_status (data_type, sync_time)
                VALUES (?, datetime('now'))
            )");

            query.bind(1, status.data_type);

            return query.exec() > 0;
        }
        catch (const std::exception &e)
        {
            SPDLOG_ERROR("SyncStatusDao::insertOrUpdate error: {}", e.what());
            return false;
        }
    }

private:
    DatabaseManager &db_manager_;
};
