#include <iostream>
#include <string>
#include <openssl/sha.h>
#include <iomanip>
#include <sstream>

std::string hashPassword(const std::string &password)
{
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256_CTX sha256;
    SHA256_Init(&sha256);
    SHA256_Update(&sha256, password.c_str(), password.length());
    SHA256_Final(hash, &sha256);

    std::stringstream ss;
    for (int i = 0; i < SHA256_DIGEST_LENGTH; i++)
    {
        ss << std::hex << std::setw(2) << std::setfill('0') << (int)hash[i];
    }
    return ss.str();
}

int main(int argc, char* argv[])
{
    if (argc != 2)
    {
        std::cout << "Usage: " << argv[0] << " <password>" << std::endl;
        std::cout << "Example: " << argv[0] << " admin123" << std::endl;
        return 1;
    }

    std::string password = argv[1];
    std::string hash = hashPassword(password);
    
    std::cout << "Password: " << password << std::endl;
    std::cout << "SHA-256 Hash: " << hash << std::endl;
    
    return 0;
}
