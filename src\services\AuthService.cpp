#include "services/AuthService.hpp"
#include <iomanip>
#include <spdlog/spdlog.h>
#include <sstream>

AuthService::AuthService(const AuthConfig &config)
    : config_(config)
    , rng_(std::chrono::steady_clock::now().time_since_epoch().count())
{
}

std::string AuthService::authenticate(const std::string &username, const std::string &password)
{
    if (username == config_.username && verifyPassword(password, config_.password))
    {
        std::string token = generateSessionToken();
        auto now = std::chrono::system_clock::now();
        auto expires = now + std::chrono::hours(config_.session_timeout_hours);

        sessions_[token] = SessionInfo{ now, expires };

        SPDLOG_INFO("User '{}' authenticated successfully", username);
        cleanupExpiredSessions();
        return token;
    }

    SPDLOG_WARN("Authentication failed for user '{}'", username);
    return "";
}

bool AuthService::validateSession(const std::string &token)
{
    auto it = sessions_.find(token);
    if (it == sessions_.end())
    {
        return false;
    }

    if (isSessionExpired(token))
    {
        sessions_.erase(it);
        return false;
    }

    return true;
}

void AuthService::invalidateSession(const std::string &token)
{
    auto it = sessions_.find(token);
    if (it != sessions_.end())
    {
        sessions_.erase(it);
        SPDLOG_INFO("Session invalidated");
    }
}

std::string AuthService::generateSessionToken()
{
    std::uniform_int_distribution<> dis(0, 15);
    std::stringstream ss;
    ss << std::hex;

    for (int i = 0; i < 32; ++i)
    {
        ss << dis(rng_);
    }

    return ss.str();
}

bool AuthService::isSessionExpired(const std::string &token)
{
    auto it = sessions_.find(token);
    if (it == sessions_.end())
    {
        return true;
    }

    auto now = std::chrono::system_clock::now();
    return now > it->second.expires_at;
}

void AuthService::cleanupExpiredSessions()
{
    auto now = std::chrono::system_clock::now();
    auto it = sessions_.begin();

    while (it != sessions_.end())
    {
        if (now > it->second.expires_at)
        {
            it = sessions_.erase(it);
        }
        else
        {
            ++it;
        }
    }
}

std::string AuthService::hashPassword(const std::string &password)
{
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256_CTX sha256;
    SHA256_Init(&sha256);
    SHA256_Update(&sha256, password.c_str(), password.length());
    SHA256_Final(hash, &sha256);

    std::stringstream ss;
    for (int i = 0; i < SHA256_DIGEST_LENGTH; i++)
    {
        ss << std::hex << std::setw(2) << std::setfill('0') << (int)hash[i];
    }
    return ss.str();
}

bool AuthService::verifyPassword(const std::string &password, const std::string &hash)
{
    // If the stored hash doesn't look like a SHA-256 hash (64 hex chars),
    // assume it's a plain text password for backward compatibility
    if (hash.length() != 64)
    {
        SPDLOG_WARN("Password appears to be stored in plain text. Consider updating to hashed format.");
        return password == hash;
    }

    std::string computed_hash = hashPassword(password);
    return computed_hash == hash;
}
