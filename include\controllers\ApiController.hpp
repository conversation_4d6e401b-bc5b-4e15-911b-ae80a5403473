#pragma once

#include "core/Application.hpp"
#include "dao/CustomerDao.hpp"
#include "dao/HolidayDao.hpp"
#include "dao/ProductDao.hpp"
#include "dao/ProjectDao.hpp"
#include "dao/UserDao.hpp"
#include "dao/WorkHourDao.hpp"
#include "database/DatabaseManager.hpp"
#include "services/AuthService.hpp"
#include "services/SyncService.hpp"
#include <algorithm>
#include <chrono>
#include <fstream>
#include <functional>
#include <httplib.h>
#include <iomanip>
#include <nlohmann/json.hpp>
#include <set>
#include <spdlog/spdlog.h>
#include <sstream>
#include <thread>

class ApiController
{
public:
    ApiController(Application &app)
        : app_(app)
    {
        // Set CORS headers for all responses
        auto &server = app.getServer();
        server.set_pre_routing_handler([](const httplib::Request &req, httplib::Response &res) {
            res.set_header("Access-Control-Allow-Origin", "*");
            res.set_header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
            res.set_header("Access-Control-Allow-Headers", "Content-Type, Authorization");
            return httplib::Server::HandlerResponse::Unhandled;
        });

        // Handle OPTIONS requests for CORS
        server.Options(".*", [](const httplib::Request &req, httplib::Response &res) { return; });

        // Register API routes - authentication is now handled globally by middleware
        server.Get("/api/products", [this](const httplib::Request &req, httplib::Response &res) { getProducts(req, res); });
        server.Get("/api/projects", [this](const httplib::Request &req, httplib::Response &res) { getProjects(req, res); });
        server.Get("/api/customers", [this](const httplib::Request &req, httplib::Response &res) { getCustomers(req, res); });
        server.Get("/api/users", [this](const httplib::Request &req, httplib::Response &res) { getUsers(req, res); });
        server.Get("/api/holidays", [this](const httplib::Request &req, httplib::Response &res) { getHolidays(req, res); });
        server.Get(R"(/api/holidays/year/(\d+) )", [this](const httplib::Request &req, httplib::Response &res) { getHolidaysByYear(req, res); });
        server.Get("/api/workhours/range", [this](const httplib::Request &req, httplib::Response &res) { getWorkHoursByRange(req, res); });
        server.Post("/api/workhours/submit", [this](const httplib::Request &req, httplib::Response &res) { submitWorkHour(req, res); });
        server.Get("/api/statistics/workhours", [this](const httplib::Request &req, httplib::Response &res) { getWorkHourStatistics(req, res); });
        server.Get("/api/sync/status", [this](const httplib::Request &req, httplib::Response &res) { getSyncStatus(req, res); });
        server.Get("/api/db/tables", [this](const httplib::Request &req, httplib::Response &res) { getTables(req, res); });
        server.Post("/api/db/query", [this](const httplib::Request &req, httplib::Response &res) { executeDbQuery(req, res); });
        server.Get(R"(/api/db/table/:table)", [this](const httplib::Request &req, httplib::Response &res) { getTableData(req, res); });
        server.Get("/api/config", [this](const httplib::Request &req, httplib::Response &res) { getConfig(req, res); });
        server.Post("/api/config", [this](const httplib::Request &req, httplib::Response &res) { saveConfig(req, res); });
        server.Post("/api/restart", [this](auto &&req, auto &&res) { restartApplication(req, res);});
    }

private:

    static nlohmann::json createResponse(int code, const std::string &message, const nlohmann::json &data = nlohmann::json::object())
    {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);

        std::stringstream ss;
        ss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%SZ");

        return nlohmann::json{ { "code", code }, { "message", message }, { "data", data }, { "timestamp", ss.str() } };
    }

    static void jsonResponse(httplib::Response &res, const nlohmann::json &json, int status_code = 200)
    {
        res.status = status_code;
        res.set_header("Content-Type", "application/json");
        res.body = json.dump();
    }

    void getProducts(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            auto items = app_.getProductDao().findAll();
            auto response = createResponse(0, "success", items);
            jsonResponse(res, response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            jsonResponse(res, response, 500);
        }
    }

    void getProjects(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            auto items = app_.getProjectDao().findAll();
            auto response = createResponse(0, "success", items);
            jsonResponse(res, response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            jsonResponse(res, response, 500);
        }
    }

    void getCustomers(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            auto items = app_.getCustomerDao().findAll();
            auto response = createResponse(0, "success", items);
            jsonResponse(res, response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            jsonResponse(res, response, 500);
        }
    }

    void getUsers(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            auto items = app_.getUserDao().findAll();
            auto response = createResponse(0, "success", items);
            jsonResponse(res, response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            jsonResponse(res, response, 500);
        }
    }

    void getHolidays(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            auto items = app_.getHolidayDao().findAll();
            auto response = createResponse(0, "success", items);
            jsonResponse(res, response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            jsonResponse(res, response, 500);
        }
    }

    void getHolidaysByYear(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            auto matches = req.matches;
            int year = std::stoi(matches[1]);
            auto &dao = app_.getHolidayDao();
            auto holidays = dao.findByYear(year);
            auto response = createResponse(0, "success", holidays);
            jsonResponse(res, response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            jsonResponse(res, response, 500);
        }
    }

    void getSyncStatus(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            auto &service = app_.getSyncService();
            auto statuses = service.getSyncStatus();
            auto response = createResponse(0, "success", statuses);
            jsonResponse(res, response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            jsonResponse(res, response, 500);
        }
    }

    void getWorkHoursByRange(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            std::string startDate = req.get_param_value("start_date");
            std::string endDate = req.get_param_value("end_date");

            if (startDate.empty() || endDate.empty())
            {
                auto response = createResponse(1, "start_date and end_date parameters are required");
                jsonResponse(res, response, 400);
                return;
            }

            auto &dao = app_.getWorkHourDao();
            auto workHours = dao.findByDateRange(startDate, endDate);
            auto response = createResponse(0, "success", workHours);
            jsonResponse(res, response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            jsonResponse(res, response, 500);
        }
    }

    void submitWorkHour(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            if (req.body.empty())
            {
                auto response = createResponse(1, "Request body is required");
                jsonResponse(res, response, 400);
                return;
            }

            auto workHourData = nlohmann::json::parse(req.body);

            // Validate required fields
            if (!workHourData.contains("employeeId") || !workHourData.contains("employeeName") || !workHourData.contains("submitDate") ||
                !workHourData.contains("timeConsumedList"))
            {
                auto response = createResponse(1, "Missing required fields: employeeId, employeeName, submitDate, timeConsumedList");
                jsonResponse(res, response, 400);
                return;
            }

            auto &service = app_.getSyncService();
            bool success = service.submitWorkHour(workHourData);

            auto response = createResponse(success ? 0 : 1, success ? "Work hour submitted successfully" : "Failed to submit work hour");
            jsonResponse(res, response, success ? 200 : 500);
        }
        catch (const nlohmann::json::parse_error &e)
        {
            auto response = createResponse(1, "Invalid JSON format: " + std::string(e.what()));
            jsonResponse(res, response, 400);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            jsonResponse(res, response, 500);
        }
    }

    void getWorkHourStatistics(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            std::string startDate = req.get_param_value("start_date");
            std::string endDate = req.get_param_value("end_date");

            if (startDate.empty() || endDate.empty())
            {
                auto response = createResponse(1, "start_date and end_date parameters are required");
                jsonResponse(res, response, 400);
                return;
            }

            auto &dao = app_.getWorkHourDao();
            auto statistics = dao.getStatistics(startDate, endDate);

            auto response = createResponse(0, "success", statistics);
            jsonResponse(res, response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            jsonResponse(res, response, 500);
        }
    }

    void getTables(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            auto &db = app_.getDatabaseManager().getDatabase();
            SQLite::Statement query(db, "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name");

            nlohmann::json tables = nlohmann::json::array();
            while (query.executeStep())
            {
                std::string tableName = query.getColumn(0).getString();
                tables.push_back(tableName);
            }

            auto response = createResponse(0, "success", tables);
            jsonResponse(res, response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            jsonResponse(res, response, 500);
        }
    }

    void executeDbQuery(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            auto json = nlohmann::json::parse(req.body);
            std::string sql = json.value("sql", "");

            if (sql.empty())
            {
                auto response = createResponse(1, "SQL query is required");
                jsonResponse(res, response, 400);
                return;
            }

            auto &db = app_.getDatabaseManager().getDatabase();

            // Check if it's a SELECT query (read-only for safety)
            std::string sqlUpper = sql;
            std::transform(sqlUpper.begin(), sqlUpper.end(), sqlUpper.begin(), ::toupper);
            if (sqlUpper.find("SELECT") != 0)
            {
                auto response = createResponse(1, "Only SELECT queries are allowed");
                jsonResponse(res, response, 400);
                return;
            }

            SQLite::Statement query(db, sql);

            nlohmann::json result = nlohmann::json::object();
            result["columns"] = nlohmann::json::array();
            result["rows"] = nlohmann::json::array();

            // Get column names
            int columnCount = query.getColumnCount();
            for (int i = 0; i < columnCount; i++)
            {
                result["columns"].push_back(query.getColumnName(i));
            }

            // Get data rows
            while (query.executeStep())
            {
                nlohmann::json row = nlohmann::json::array();
                for (int i = 0; i < columnCount; i++)
                {
                    if (query.getColumn(i).isNull())
                    {
                        row.push_back(nullptr);
                    }
                    else
                    {
                        row.push_back(query.getColumn(i).getString());
                    }
                }
                result["rows"].push_back(row);
            }

            auto response = createResponse(0, "success", result);
            jsonResponse(res, response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            jsonResponse(res, response, 500);
        }
    }

    void getTableData(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            std::string tableName = req.path_params.at("table");
            auto &db = app_.getDatabaseManager().getDatabase();

            // Validate table name exists
            SQLite::Statement checkQuery(db, "SELECT name FROM sqlite_master WHERE type='table' AND name=?");
            checkQuery.bind(1, tableName);

            if (!checkQuery.executeStep())
            {
                auto response = createResponse(1, "Table not found");
                jsonResponse(res, response, 404);
                return;
            }

            // Get table data
            std::string sql = "SELECT * FROM " + tableName;
            SQLite::Statement query(db, sql);

            nlohmann::json result = nlohmann::json::object();
            result["columns"] = nlohmann::json::array();
            result["rows"] = nlohmann::json::array();

            // Get column names
            int columnCount = query.getColumnCount();
            for (int i = 0; i < columnCount; i++)
            {
                result["columns"].push_back(query.getColumnName(i));
            }

            // Get data rows
            while (query.executeStep())
            {
                nlohmann::json row = nlohmann::json::array();
                for (int i = 0; i < columnCount; i++)
                {
                    if (query.getColumn(i).isNull())
                    {
                        row.push_back(nullptr);
                    }
                    else
                    {
                        row.push_back(query.getColumn(i).getString());
                    }
                }
                result["rows"].push_back(row);
            }

            auto response = createResponse(0, "success", result);
            jsonResponse(res, response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            jsonResponse(res, response, 500);
        }
    }

    void getConfig(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            std::ifstream configFile("config.json");
            if (!configFile.is_open())
            {
                auto response = createResponse(1, "Cannot open config.json");
                jsonResponse(res, response, 500);
                return;
            }

            nlohmann::json config;
            configFile >> config;
            configFile.close();

            auto response = createResponse(0, "success", config);
            jsonResponse(res, response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            jsonResponse(res, response, 500);
        }
    }

    void saveConfig(const httplib::Request &req, httplib::Response &res)
    {
        try
        {
            if (req.body.empty())
            {
                auto response = createResponse(1, "Request body is required");
                jsonResponse(res, response, 400);
                return;
            }

            auto newConfig = nlohmann::json::parse(req.body);

            // Validate required fields (holiday_api is optional)
            if (!newConfig.contains("server") || !newConfig.contains("database") || !newConfig.contains("backend") || !newConfig.contains("sync"))
            {
                auto response = createResponse(1, "Missing required configuration sections");
                jsonResponse(res, response, 400);
                return;
            }

            // Write to config file
            std::ofstream outFile("config.json");
            if (!outFile.is_open())
            {
                auto response = createResponse(1, "Cannot open config.json for writing");
                jsonResponse(res, response, 500);
                return;
            }

            outFile << newConfig.dump(4);
            outFile.close();

            auto response = createResponse(0, "Configuration saved successfully");
            jsonResponse(res, response);
        }
        catch (const nlohmann::json::parse_error &e)
        {
            auto response = createResponse(1, "Invalid JSON format: " + std::string(e.what()));
            jsonResponse(res, response, 400);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            jsonResponse(res, response, 500);
        }
    }

    void restartApplication(const httplib::Request &req, httplib::Response &res)
    {
        // try
        // {
        //     auto response = createResponse(0, "Application restart initiated");
        //     jsonResponse(res, response);
        //     app_.reset(new Application);
        // }
        // catch (const std::exception &e)
        // {
        //     auto response = createResponse(1, "Error: " + std::string(e.what()));
        //     jsonResponse(res, response, 500);
        // }
    }

    Application &app_;
};

// Inline implementations
// Authentication is now handled globally by Application middleware
