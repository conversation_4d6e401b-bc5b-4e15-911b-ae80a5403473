#include "core/Application.hpp"
#include "controllers/ApiController.hpp"
#include "controllers/AuthController.hpp"
#include "controllers/StaticController.hpp"
#include "dao/CustomerDao.hpp"
#include "dao/HolidayDao.hpp"
#include "dao/ProductDao.hpp"
#include "dao/ProjectDao.hpp"
#include "dao/SyncStatusDao.hpp"
#include "dao/UserDao.hpp"
#include "dao/WorkHourDao.hpp"
#include "services/AuthService.hpp"
#include "services/SyncService.hpp"
#include <spdlog/spdlog.h>

Application::Application(const std::string &config_file)
    : config_file_(config_file)
    , running_(false)
{
}

Application::~Application()
{
    stop();
    database_manager_.close();
}

bool Application::initialize()
{
    SPDLOG_INFO("Initializing Daily Report System...");

    {
        std::ifstream configFile("config.json");
        config_ = nlohmann::json::parse(configFile);
    }

    if (!database_manager_.initialize(config_.database.path))
    {
        SPDLOG_ERROR("Failed to initialize database");
        return false;
    }

    // Initialize DAO components after database is ready
    initializeDaos();

    SPDLOG_INFO("Application initialized successfully");
    return true;
}

void Application::start()
{
    if (initialize())
    {
        // Initialize auth service first
        auth_service_ = std::make_shared<AuthService>(config_.auth);

        // Setup global authentication middleware
        setupAuthMiddleware();

        // Initialize controllers
        auth_ctrl_ = std::make_shared<AuthController>(*this);
        api_ctrl_ = std::make_shared<ApiController>(*this);
        file_ctrl_ = std::make_shared<StaticController>(*this);
        sync_service_ = std::make_shared<SyncService>(*this);

        startServer();
    }
}

void Application::stop()
{
    if (running_)
    {
        running_ = false;
        server_.stop();
        if (server_thread_.joinable())
        {
            server_thread_.join();
        }
        SPDLOG_INFO("Server stopped");
    }
}

void Application::startServer()
{
    running_ = true;
    SPDLOG_INFO("Starting server on {}:{}", config_.server.host, config_.server.port);
    // server_.set_logger([](auto&& req, auto&& rep) {
    //     SPDLOG_INFO("REQUEST: url={}\nbody={}", req.path, std::string(req.body));
    //     SPDLOG_INFO("RESPONSE: body={}", rep.body);
    // });
    server_.set_error_handler([](auto &&req, auto &&rep) { SPDLOG_ERROR("Request {} error", std::string(req.path)); });

    server_thread_ = std::thread([this]() {
        if (!server_.listen(config_.server.host, config_.server.port))
        {
            SPDLOG_ERROR("Failed to start server on {}:{}", config_.server.host, config_.server.port);
            running_ = false;
        }
    });

    SPDLOG_INFO("Server started successfully");

    // Wait for server thread to complete
    if (server_thread_.joinable())
    {
        server_thread_.join();
    }
}

void Application::setupAuthMiddleware()
{
    // Set up global authentication middleware using pre-routing handler
    server_.set_pre_routing_handler([this](const httplib::Request &req, httplib::Response &res) {
        std::string path = req.path;

        // Allow public access to authentication endpoints
        if (path.substr(0, 10) == "/api/auth/") {
            return httplib::Server::HandlerResponse::Unhandled;
        }

        // Allow public access to login.html
        if (path == "/login.html") {
            return httplib::Server::HandlerResponse::Unhandled;
        }

        // Check if this is an API endpoint that requires authentication
        if (path.substr(0, 5) == "/api/") {
            if (!isAuthenticated(req)) {
                // Return 401 for API endpoints
                res.status = 401;
                res.set_header("Content-Type", "application/json");
                res.body = R"({"code": 1, "message": "Authentication required", "timestamp": ")" + getCurrentTimestamp() + R"("})";
                return httplib::Server::HandlerResponse::Handled;
            }
        }

        // For all other requests, let the routing continue
        return httplib::Server::HandlerResponse::Unhandled;
    });
}

bool Application::isAuthenticated(const httplib::Request &req)
{
    std::string token = getSessionToken(req);
    if (token.empty()) {
        return false;
    }

    return auth_service_ && auth_service_->validateSession(token);
}

std::string Application::getSessionToken(const httplib::Request &req)
{
    // Try to get token from cookie
    auto cookie_header = req.get_header_value("Cookie");
    if (!cookie_header.empty()) {
        size_t pos = cookie_header.find("session_token=");
        if (pos != std::string::npos) {
            pos += 14; // length of "session_token="
            size_t end = cookie_header.find(';', pos);
            if (end == std::string::npos)
                end = cookie_header.length();
            return cookie_header.substr(pos, end - pos);
        }
    }

    return "";
}

std::string Application::getCurrentTimestamp()
{
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    std::stringstream ss;
    ss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%SZ");
    return ss.str();
}

void Application::initializeDaos()
{
    product_dao_ = std::make_shared<ProductDao>(database_manager_);
    project_dao_ = std::make_shared<ProjectDao>(database_manager_);
    customer_dao_ = std::make_shared<CustomerDao>(database_manager_);
    user_dao_ = std::make_shared<UserDao>(database_manager_);
    work_hour_dao_ = std::make_shared<WorkHourDao>(database_manager_);
    holiday_dao_ = std::make_shared<HolidayDao>(database_manager_);
    sync_status_dao_ = std::make_shared<SyncStatusDao>(database_manager_);
}
